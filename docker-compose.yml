version: '3.8'

services:
  pmcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pmcp-server
    restart: unless-stopped
    environment:
      # Application Environment
      APP_ENV: prod
      
      # Server Configuration
      MCP_PORT: 8000
      LOG_LEVEL: INFO
      
      # Database Configuration
      DATABASE_URL: **************************************************/pmcp_action_history
      
      # Provably API Configuration (override in .env file)
      PROVABLY_API_KEY: ${PROVABLY_API_KEY}
      PROVABLY_BASE_URL: ${PROVABLY_BASE_URL:-https://api.provably.ai}
      PROVABLY_JWT_SECRET: ${PROVABLY_JWT_SECRET}
      
      # Sentry Configuration (optional)
      SENTRY_DSN: ${SENTRY_DSN:-}
    ports:
      - "8000:8000"
    volumes:
      # Mount .env file for local secrets
      - ./.env:/app/.env:ro
      # Mount logs directory for persistent logging
      - ./logs:/app/logs
    networks:
      - pmcp-network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PMCP Prover Server (Verification Service) - Future implementation
  pmcp-prover-server:
    build:
      context: .
      dockerfile: Dockerfile.prover
    container_name: pmcp-prover-server
    restart: unless-stopped
    environment:
      APP_ENV: prod
      PROVER_PORT: 8001
      LOG_LEVEL: INFO
      DATABASE_URL: **************************************************/pmcp_action_history
    ports:
      - "8001:8001"
    volumes:
      - ./.env:/app/.env:ro
      - ./logs:/app/logs
    networks:
      - pmcp-network
    depends_on:
      postgres:
        condition: service_healthy
    profiles:
      - prover  # Only start with --profile prover
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and session management (optional)
  redis:
    image: redis:7-alpine
    container_name: pmcp-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-pmcp_redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - pmcp-network
    profiles:
      - cache  # Only start with --profile cache
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx reverse proxy for production deployment (optional)
  nginx:
    image: nginx:alpine
    container_name: pmcp-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - pmcp-network
    depends_on:
      - pmcp-action-server
    profiles:
      - production  # Only start with --profile production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  pmcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
