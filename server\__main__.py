import asyncio

from fastmcp import FastMCP
from fastmcp.server.auth import AccessToken, TokenVerifier
from loguru import logger
from starlette.requests import Request
from starlette.responses import JSONResponse

from server.core.config import Config
from server.core.logging import setup_logging
from server.middlewares import ErrorHandlingMiddleware, RequestLoggingMiddleware
from server.middlewares.api_key_middleware import APIKeyMiddleware
from server.tools import provably, greet
import prompts

instructions = (
    "This is the Provably Model Context Protocol (PMCP) Server. "
    "It acts as a trusted gateway for AI agents to interact with the Provably API. "
)

class PassthroughVerifier(TokenVerifier):
    async def verify_token(self, token: str) -> AccessToken | None:
        if not token:
            return None  # FastMCP will reject with 401
        return AccessToken(
            token=token,
            client_id="passthrough",   # you can hardcode or generate something
            scopes=[],                 # no scopes enforced right now
            expires_at=None            # static API keys don’t expire
        )

action_mcp = FastMCP(
    name="PM<PERSON>",
    version="1.0.0",
    # auth=PassthroughVerifier(),
    auth=None,
    lifespan=None,
    instructions=instructions,
    on_duplicate_tools="error",
    on_duplicate_resources="warn",
    on_duplicate_prompts="replace",
    include_fastmcp_meta=True,
)


@action_mcp.custom_route("/health", methods=["GET"])
async def health_check(request: Request) -> JSONResponse:
    return JSONResponse(
        {
            "status": "healthy",
            "service": "pmcp-mcp-server",
            "version": Config.VERSION,
        }
    )


action_mcp.add_tool(greet.send_hello_tool)

# --- Add Prompts ---
action_mcp.add_prompt(prompts.query.sql_query_generation_prompt)

# --- Add Provably tools ---
# Organizations
action_mcp.add_tool(provably.organizations.list_organizations_tool)
action_mcp.add_tool(provably.organizations.add_organization_tool)
action_mcp.add_tool(provably.organizations.get_organization_tool)
action_mcp.add_tool(provably.organizations.delete_organization_tool)
action_mcp.add_tool(provably.organizations.update_organization_tool)
action_mcp.add_tool(provably.organizations.leave_organization_tool)
action_mcp.add_tool(provably.organizations.get_users_in_organization_tool)
action_mcp.add_tool(provably.organizations.update_organization_user_tool)
action_mcp.add_tool(provably.organizations.bulk_invite_users_to_organization_tool)
action_mcp.add_tool(provably.organizations.bulk_delete_users_from_organization_tool)

# Collections
action_mcp.add_tool(provably.collections.list_collections_tool)
action_mcp.add_tool(provably.collections.create_collection_tool)
action_mcp.add_tool(provably.collections.get_collection_tool)
action_mcp.add_tool(provably.collections.update_collection_tool)
action_mcp.add_tool(provably.collections.invite_users_to_collection_tool)


# Columns
action_mcp.add_tool(provably.columns.bulk_create_columns_tool)
action_mcp.add_tool(provably.columns.list_table_columns_tool)
action_mcp.add_tool(provably.columns.get_table_column_tool)
action_mcp.add_tool(provably.columns.update_table_column_tool)
action_mcp.add_tool(provably.columns.delete_table_column_tool)
action_mcp.add_tool(provably.columns.bulk_delete_table_columns_tool)

# User
action_mcp.add_tool(provably.user.get_current_user_tool)
action_mcp.add_tool(provably.user.update_current_user_tool)
action_mcp.add_tool(provably.user.delete_current_user_tool)
action_mcp.add_tool(provably.user.intiate_delete_current_user_tool)
action_mcp.add_tool(provably.user.search_user_tool)
action_mcp.add_tool(provably.user.get_user_api_key_tool)

# Databases
action_mcp.add_tool(provably.databases.list_databases_tool)
action_mcp.add_tool(provably.databases.check_database_access_tool)
action_mcp.add_tool(provably.databases.create_database_tool)
action_mcp.add_tool(provably.databases.get_database_tool)
action_mcp.add_tool(provably.databases.update_database_tool)
action_mcp.add_tool(provably.databases.bulk_delete_databases_tool)

# Middlewares
action_mcp.add_tool(provably.middlewares.list_middlewares_tool)
action_mcp.add_tool(provably.middlewares.add_provably_middleware_tool)
action_mcp.add_tool(provably.middlewares.get_middleware_tool)
action_mcp.add_tool(provably.middlewares.update_middleware_tool)
action_mcp.add_tool(provably.middlewares.bulk_delete_middlewares_tool)

# Contents
action_mcp.add_tool(provably.contents.list_organization_contents_tool)
action_mcp.add_tool(provably.contents.search_organization_contents_tool)

# Validation
action_mcp.add_tool(provably.validation.validate_database_tool)

# Query
action_mcp.add_tool(provably.query.validate_sql_query_tool)
action_mcp.add_tool(provably.query.execute_sql_query_tool)
action_mcp.add_tool(provably.query.verify_sql_query_tool)

# Query records
action_mcp.add_tool(provably.query_records.get_query_tool)

# Proof
action_mcp.add_tool(provably.proof.download_proof_tool)

# Data hierarchy
action_mcp.add_tool(provably.data_hierarchy.get_data_hierarchy_tool)

# Feedback
action_mcp.add_tool(provably.feedback.create_feedback_tool)

# User Notifications
action_mcp.add_tool(provably.notifications.get_user_notifications_tool)

# Organization Logs
action_mcp.add_tool(provably.organization_logs.get_organization_logs_tool)

# Organization Notifications
action_mcp.add_tool(
    provably.organization_notifications.get_organization_notifications_tool
)
action_mcp.add_tool(
    provably.organization_notifications.get_organization_notifications_settings_tool
)
action_mcp.add_tool(
    provably.organization_notifications.update_organization_notifications_settings_tool
)
action_mcp.add_tool(
    provably.organization_notifications.get_organization_notifications_count_tool
)

#  Public
action_mcp.add_tool(provably.public.subscribe_to_newsletter_tool)

# Schemas
action_mcp.add_tool(provably.schemas.bulk_create_database_schemas_tool)
action_mcp.add_tool(provably.schemas.get_database_schema_tool)
action_mcp.add_tool(provably.schemas.update_database_schema_tool)
action_mcp.add_tool(provably.schemas.get_database_schemas_tool)
action_mcp.add_tool(provably.schemas.bulk_delete_database_schemas_tool)

# Tables
action_mcp.add_tool(provably.tables.get_database_tables_tool)
action_mcp.add_tool(provably.tables.get_database_table_tool)
action_mcp.add_tool(provably.tables.update_database_table_tool)
action_mcp.add_tool(provably.tables.bulk_delete_database_tables_tool)

# --- ---

# Middlewares
action_mcp.add_middleware(ErrorHandlingMiddleware())
action_mcp.add_middleware(RequestLoggingMiddleware())
action_mcp.add_middleware(APIKeyMiddleware())


async def main():
    # --- On Startup ---
    setup_logging()
    logger.info("Starting PMCP Server")

    # _ = await init_db()

    logger.success(
        f"{Config.PROJECT_NAME} v{Config.VERSION} started successfully in {Config.ENVIRONMENT} mode."
    )

    await action_mcp.run_async(
        transport="http",
        host=Config.MCP_HOST,
        port=Config.MCP_PORT,
        path="/pmcp",
        show_banner=False,
        log_level="critical",
    )

    # --- On Shutdown ---
    logger.info("Application is shutting down...")


if __name__ == "__main__":
    asyncio.run(main())
