# Use Python 3.13 slim image as base
FROM python:3.13-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    --no-install-recommends \
    libpq5 \
    && apt-get clean
    && rm -rf /var/lib/apt/lists/*

# Install UV package manager
RUN pip install uv

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install Python dependencies using UV
RUN uv sync --frozen --no-dev

# Add UV's Python environment to PATH
ENV PATH="/app/.venv/bin:$PATH"

# Copy application code
COPY . .

# Create non-root user for security
RUN groupadd -r pmcpgroup && useradd --no-log-init -r -g pmcpgroup pmcp
RUN chown -R pmcp:pmcpgroup /app
USER pmcp

# Expose port
EXPOSE 8000

# Set default environment
ENV APP_ENV=prod

# Run the PMCP Action Server
CMD ["uv", "run", "python", "-m", "server"]

