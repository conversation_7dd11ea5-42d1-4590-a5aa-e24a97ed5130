from fastmcp.server.dependencies import get_access_token, get_http_headers
from fastmcp.server.middleware import <PERSON>Next, Middleware, MiddlewareContext

from server.core.config import Config
from server.core.exceptions import PMCPPermissionError


class APIKeyMiddleware(Middleware):
    """Temporary middleware to extract and validate API keys from request headers."""

    async def on_call_tool(self, context: MiddlewareContext, call_next: CallNext):
        """Extract API key and store in context."""
        
        access_token = get_access_token()
        print(access_token)
        headers = get_http_headers()
        print("HEADERS")
        print(headers)

        api_key = Config.PROVABLY_API_KEY.get_secret_value()
        jwt_secret = Config.PROVABLY_JWT_SECRET.get_secret_value()

        context.fastmcp_context.set_state("api_key", api_key)
        context.fastmcp_context.set_state("jwt_secret", jwt_secret)

        if not api_key:
            raise PMCPPermissionError("API key required")

        return await call_next(context)
